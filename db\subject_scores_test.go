package db

import (
	"context"
	"fmt"
	"testing"
	"time"
	"ziaacademy-backend/internal/models"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestCalculateSubjectAndTopicScores(t *testing.T) {
	// Skip if no test database is available
	if testDB == nil {
		t.Skip("Test database not available")
	}

	// Generate unique names to avoid conflicts
	timestamp := time.Now().UnixNano()
	mathSubjectName := fmt.Sprintf("SubjectScoreTestMath_%d", timestamp)
	physicsSubjectName := fmt.Sprintf("SubjectScoreTestPhysics_%d", timestamp)
	testName := fmt.Sprintf("SubjectScoreTest_%d", timestamp)

	// Clean up after test
	defer func() {
		testDB.Exec("DELETE FROM test_responses WHERE test_id IN (SELECT id FROM tests WHERE name = ?)", testName)
		testDB.Exec("DELETE FROM students WHERE user_id IN (SELECT id FROM users WHERE email LIKE ?)", fmt.Sprintf("%%<EMAIL>", timestamp))
		testDB.Exec("DELETE FROM users WHERE email LIKE ?", fmt.Sprintf("%%<EMAIL>", timestamp))
		testDB.Exec("DELETE FROM questions WHERE topic_id IN (SELECT id FROM topics WHERE name LIKE ?)", fmt.Sprintf("%%SubjectScoreTest_%d%%", timestamp))
		testDB.Exec("DELETE FROM topics WHERE chapter_id IN (SELECT id FROM chapters WHERE name LIKE ?)", fmt.Sprintf("%%SubjectScoreTest_%d%%", timestamp))
		testDB.Exec("DELETE FROM chapters WHERE subject_id IN (SELECT id FROM subjects WHERE name IN (?, ?))", mathSubjectName, physicsSubjectName)
		testDB.Exec("DELETE FROM tests WHERE name = ?", testName)
		testDB.Exec("DELETE FROM test_types WHERE name LIKE ?", fmt.Sprintf("%%TestType_%d%%", timestamp))
		testDB.Exec("DELETE FROM difficulties WHERE name LIKE ?", fmt.Sprintf("%%TestDifficulty_%d%%", timestamp))
		testDB.Exec("DELETE FROM subjects WHERE name IN (?, ?)", mathSubjectName, physicsSubjectName)
	}()

	// Create test data
	subject1 := models.Subject{Name: mathSubjectName, DisplayName: "Mathematics"}
	err := testDB.Create(&subject1).Error
	require.NoError(t, err)

	subject2 := models.Subject{Name: physicsSubjectName, DisplayName: "Physics"}
	err = testDB.Create(&subject2).Error
	require.NoError(t, err)

	chapter1 := models.Chapter{Name: fmt.Sprintf("Algebra_%d", timestamp), DisplayName: "Algebra", SubjectID: subject1.ID}
	err = testDB.Create(&chapter1).Error
	require.NoError(t, err)

	chapter2 := models.Chapter{Name: fmt.Sprintf("Mechanics_%d", timestamp), DisplayName: "Mechanics", SubjectID: subject2.ID}
	err = testDB.Create(&chapter2).Error
	require.NoError(t, err)

	topic1 := models.Topic{Name: fmt.Sprintf("LinearEquations_%d", timestamp), ChapterID: chapter1.ID}
	err = testDB.Create(&topic1).Error
	require.NoError(t, err)

	topic2 := models.Topic{Name: fmt.Sprintf("QuadraticEquations_%d", timestamp), ChapterID: chapter1.ID}
	err = testDB.Create(&topic2).Error
	require.NoError(t, err)

	topic3 := models.Topic{Name: fmt.Sprintf("NewtonsLaws_%d", timestamp), ChapterID: chapter2.ID}
	err = testDB.Create(&topic3).Error
	require.NoError(t, err)

	// Create a difficulty (assuming it exists or create one)
	difficulty := models.Difficulty{Name: fmt.Sprintf("TestDifficulty_%d", timestamp)}
	err = testDB.Create(&difficulty).Error
	require.NoError(t, err)

	question1 := models.Question{Text: "Q1", TopicID: topic1.ID, DifficultyID: difficulty.ID, QuestionType: "mcq"}
	err = testDB.Create(&question1).Error
	require.NoError(t, err)

	question2 := models.Question{Text: "Q2", TopicID: topic2.ID, DifficultyID: difficulty.ID, QuestionType: "mcq"}
	err = testDB.Create(&question2).Error
	require.NoError(t, err)

	question3 := models.Question{Text: "Q3", TopicID: topic3.ID, DifficultyID: difficulty.ID, QuestionType: "mcq"}
	err = testDB.Create(&question3).Error
	require.NoError(t, err)

	// Create a test type (assuming it exists or create one)
	testType := models.TestType{Name: fmt.Sprintf("TestType_%d", timestamp)}
	err = testDB.Create(&testType).Error
	require.NoError(t, err)

	test := models.Test{Name: testName, TestTypeID: testType.ID}
	err = testDB.Create(&test).Error
	require.NoError(t, err)

	// Create test students
	user1 := models.User{
		FullName:    "Test Student 1",
		Email:       fmt.Sprintf("<EMAIL>", timestamp),
		PhoneNumber: fmt.Sprintf("123456%04d", timestamp%10000),
		Role:        "student",
	}
	err = testDB.Create(&user1).Error
	require.NoError(t, err)

	student1 := models.Student{
		UserID:      user1.ID,
		ParentPhone: fmt.Sprintf("098765%04d", timestamp%10000),
		ParentEmail: fmt.Sprintf("<EMAIL>", timestamp),
	}
	err = testDB.Create(&student1).Error
	require.NoError(t, err)

	user2 := models.User{
		FullName:    "Test Student 2",
		Email:       fmt.Sprintf("<EMAIL>", timestamp),
		PhoneNumber: fmt.Sprintf("123457%04d", timestamp%10000),
		Role:        "student",
	}
	err = testDB.Create(&user2).Error
	require.NoError(t, err)

	student2 := models.Student{
		UserID:      user2.ID,
		ParentPhone: fmt.Sprintf("098766%04d", timestamp%10000),
		ParentEmail: fmt.Sprintf("<EMAIL>", timestamp),
	}
	err = testDB.Create(&student2).Error
	require.NoError(t, err)

	// Create test responses with calculated scores
	score1 := 4
	score2 := 3
	score3 := 5
	score4 := 2

	responses := []models.TestResponse{
		{TestID: test.ID, QuestionID: question1.ID, StudentID: student1.ID, CalculatedScore: &score1}, // Math/Linear: 4
		{TestID: test.ID, QuestionID: question1.ID, StudentID: student2.ID, CalculatedScore: &score2}, // Math/Linear: 3
		{TestID: test.ID, QuestionID: question2.ID, StudentID: student1.ID, CalculatedScore: &score3}, // Math/Quadratic: 5
		{TestID: test.ID, QuestionID: question3.ID, StudentID: student1.ID, CalculatedScore: &score4}, // Physics/Newton: 2
	}

	for _, response := range responses {
		err = testDB.Create(&response).Error
		require.NoError(t, err)
	}

	// Create DbPlugin instance
	plugin := NewDbPlugin(testDB)

	// Test the function
	result, err := plugin.calculateSubjectAndTopicScores(context.Background(), test.ID)
	require.NoError(t, err)
	require.NotNil(t, result)

	// Verify Mathematics subject scores
	mathScores, exists := result[mathSubjectName]
	assert.True(t, exists, "Mathematics subject should exist in results")

	// Mathematics average: (4 + 3 + 5) / 3 = 4.0
	assert.Equal(t, 4.0, mathScores.AverageScore, "Mathematics average score should be 4.0")

	// Verify topic scores within Mathematics
	linearTopicName := fmt.Sprintf("LinearEquations_%d", timestamp)
	quadraticTopicName := fmt.Sprintf("QuadraticEquations_%d", timestamp)

	assert.Contains(t, mathScores.TopicScores, linearTopicName)
	assert.Contains(t, mathScores.TopicScores, quadraticTopicName)

	// Linear Equations average: (4 + 3) / 2 = 3.5
	assert.Equal(t, 3.5, mathScores.TopicScores[linearTopicName], "Linear Equations average should be 3.5")

	// Quadratic Equations average: 5 / 1 = 5.0
	assert.Equal(t, 5.0, mathScores.TopicScores[quadraticTopicName], "Quadratic Equations average should be 5.0")

	// Verify Physics subject scores
	physicsScores, exists := result[physicsSubjectName]
	assert.True(t, exists, "Physics subject should exist in results")

	// Physics average: 2 / 1 = 2.0
	assert.Equal(t, 2.0, physicsScores.AverageScore, "Physics average score should be 2.0")

	// Verify topic scores within Physics
	newtonsTopicName := fmt.Sprintf("NewtonsLaws_%d", timestamp)
	assert.Contains(t, physicsScores.TopicScores, newtonsTopicName)

	// Newton's Laws average: 2 / 1 = 2.0
	assert.Equal(t, 2.0, physicsScores.TopicScores[newtonsTopicName], "Newton's Laws average should be 2.0")
}

func TestCalculateSubjectAndTopicScoresEmptyResponses(t *testing.T) {
	// Skip if no test database is available
	if testDB == nil {
		t.Skip("Test database not available")
	}

	timestamp := time.Now().UnixNano()
	testName := fmt.Sprintf("EmptySubjectScoreTest_%d", timestamp)

	// Clean up after test
	defer func() {
		testDB.Exec("DELETE FROM tests WHERE name = ?", testName)
	}()

	// Create a test type (assuming it exists or create one)
	testType := models.TestType{Name: fmt.Sprintf("EmptyTestType_%d", timestamp)}
	err := testDB.Create(&testType).Error
	require.NoError(t, err)

	test := models.Test{Name: testName, TestTypeID: testType.ID}
	err = testDB.Create(&test).Error
	require.NoError(t, err)

	// Create DbPlugin instance
	plugin := NewDbPlugin(testDB)

	// Test the function with no responses
	result, err := plugin.calculateSubjectAndTopicScores(context.Background(), test.ID)
	require.NoError(t, err)
	require.NotNil(t, result)
	assert.Empty(t, result, "Result should be empty when no responses exist")
}

func TestGetTestRankingsIncludesSubjectScores(t *testing.T) {
	// Skip if no test database is available
	if testDB == nil {
		t.Skip("Test database not available")
	}

	// Generate unique names to avoid conflicts
	timestamp := time.Now().UnixNano()
	mathSubjectName := fmt.Sprintf("RankingTestMath_%d", timestamp)
	testName := fmt.Sprintf("RankingTest_%d", timestamp)

	// Clean up after test
	defer func() {
		testDB.Exec("DELETE FROM test_responses WHERE test_id IN (SELECT id FROM tests WHERE name = ?)", testName)
		testDB.Exec("DELETE FROM student_test_marks WHERE test_id IN (SELECT id FROM tests WHERE name = ?)", testName)
		testDB.Exec("DELETE FROM students WHERE user_id IN (SELECT id FROM users WHERE email LIKE ?)", fmt.Sprintf("%%<EMAIL>", timestamp))
		testDB.Exec("DELETE FROM users WHERE email LIKE ?", fmt.Sprintf("%%<EMAIL>", timestamp))
		testDB.Exec("DELETE FROM questions WHERE topic_id IN (SELECT id FROM topics WHERE name LIKE ?)", fmt.Sprintf("%%RankingTest_%d%%", timestamp))
		testDB.Exec("DELETE FROM topics WHERE chapter_id IN (SELECT id FROM chapters WHERE name LIKE ?)", fmt.Sprintf("%%RankingTest_%d%%", timestamp))
		testDB.Exec("DELETE FROM chapters WHERE subject_id IN (SELECT id FROM subjects WHERE name = ?)", mathSubjectName)
		testDB.Exec("DELETE FROM tests WHERE name = ?", testName)
		testDB.Exec("DELETE FROM test_types WHERE name LIKE ?", fmt.Sprintf("%%RankingTestType_%d%%", timestamp))
		testDB.Exec("DELETE FROM difficulties WHERE name LIKE ?", fmt.Sprintf("%%RankingTestDifficulty_%d%%", timestamp))
		testDB.Exec("DELETE FROM subjects WHERE name = ?", mathSubjectName)
	}()

	// Create minimal test data for ranking test
	subject := models.Subject{Name: mathSubjectName, DisplayName: "Mathematics"}
	err := testDB.Create(&subject).Error
	require.NoError(t, err)

	chapter := models.Chapter{Name: fmt.Sprintf("RankingTestChapter_%d", timestamp), DisplayName: "Algebra", SubjectID: subject.ID}
	err = testDB.Create(&chapter).Error
	require.NoError(t, err)

	topic := models.Topic{Name: fmt.Sprintf("RankingTestTopic_%d", timestamp), ChapterID: chapter.ID}
	err = testDB.Create(&topic).Error
	require.NoError(t, err)

	difficulty := models.Difficulty{Name: fmt.Sprintf("RankingTestDifficulty_%d", timestamp)}
	err = testDB.Create(&difficulty).Error
	require.NoError(t, err)

	question := models.Question{Text: "Test Question", TopicID: topic.ID, DifficultyID: difficulty.ID, QuestionType: "mcq"}
	err = testDB.Create(&question).Error
	require.NoError(t, err)

	testType := models.TestType{Name: fmt.Sprintf("RankingTestType_%d", timestamp)}
	err = testDB.Create(&testType).Error
	require.NoError(t, err)

	test := models.Test{Name: testName, TestTypeID: testType.ID}
	err = testDB.Create(&test).Error
	require.NoError(t, err)

	// Create a test student
	user := models.User{
		FullName:    "Ranking Test Student",
		Email:       fmt.Sprintf("<EMAIL>", timestamp),
		PhoneNumber: fmt.Sprintf("123456%04d", timestamp%10000),
		Role:        "student",
	}
	err = testDB.Create(&user).Error
	require.NoError(t, err)

	student := models.Student{
		UserID:      user.ID,
		ParentPhone: fmt.Sprintf("098765%04d", timestamp%10000),
		ParentEmail: fmt.Sprintf("<EMAIL>", timestamp),
	}
	err = testDB.Create(&student).Error
	require.NoError(t, err)

	// Create test response and student test mark
	score := 5
	response := models.TestResponse{
		TestID:          test.ID,
		QuestionID:      question.ID,
		StudentID:       student.ID,
		CalculatedScore: &score,
	}
	err = testDB.Create(&response).Error
	require.NoError(t, err)

	studentTestMark := models.StudentTestMark{
		StudentID:          int(student.ID),
		TestID:             int(test.ID),
		TotalPositiveMarks: 5,
		TotalNegativeMarks: 0,
	}
	err = testDB.Create(&studentTestMark).Error
	require.NoError(t, err)

	// Test GetTestRankings function
	plugin := NewDbPlugin(testDB)
	result, err := plugin.GetTestRankings(context.Background(), test.ID, 0, 0)
	require.NoError(t, err)
	require.NotNil(t, result)

	// Verify that SubjectScores is included and populated
	assert.NotNil(t, result.SubjectScores, "SubjectScores should not be nil")
	assert.Contains(t, result.SubjectScores, mathSubjectName, "Should contain the math subject")

	mathScores := result.SubjectScores[mathSubjectName]
	assert.Equal(t, 5.0, mathScores.AverageScore, "Math average score should be 5.0")
	assert.Contains(t, mathScores.TopicScores, fmt.Sprintf("RankingTestTopic_%d", timestamp), "Should contain the topic")
}
