package db

import (
	"context"
	"fmt"
	"log/slog"
	"time"
	"ziaacademy-backend/internal/models"

	"gorm.io/gorm"
)

// RecordTestResponses records all responses for a test from a student
func (p *DbPlugin) RecordTestResponses(ctx context.Context, studentID uint, testResponsesInput *models.TestResponsesForCreate) (*models.TestResponsesResult, error) {
	start := time.Now()

	slog.Info("Recording test responses",
		"student_id", studentID,
		"test_id", testResponsesInput.TestID,
		"response_count", len(testResponsesInput.Responses),
	)

	// Verify the test exists and is active (read-only operation, no transaction needed)
	var test models.Test
	if err := p.db.Preload("Sections.Questions.Options").First(&test, testResponsesInput.TestID).Error; err != nil {
		duration := time.Since(start)
		slog.Error("Test not found for response recording",
			"student_id", studentID,
			"test_id", testResponsesInput.TestID,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return nil, fmt.Errorf("test with ID %d not found: %w", testResponsesInput.TestID, err)
	}

	if !test.Active {
		duration := time.Since(start)
		slog.Warn("Attempt to submit responses to inactive test",
			"student_id", studentID,
			"test_id", testResponsesInput.TestID,
			"duration_ms", duration.Milliseconds(),
		)
		return nil, fmt.Errorf("test with ID %d is not active", testResponsesInput.TestID)
	}

	// Verify the student exists (read-only operation, no transaction needed)
	var student models.Student
	if err := p.db.First(&student, studentID).Error; err != nil {
		duration := time.Since(start)
		slog.Error("Student not found for response recording",
			"student_id", studentID,
			"test_id", testResponsesInput.TestID,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return nil, fmt.Errorf("student with ID %d not found: %w", studentID, err)
	}

	// Check if student has already submitted responses for this test (read-only operation)
	var existingResponseCount int64
	if err := p.db.Model(&models.TestResponse{}).
		Where("student_id = ? AND test_id = ?", studentID, testResponsesInput.TestID).
		Count(&existingResponseCount).Error; err != nil {
		return nil, fmt.Errorf("failed to check existing responses: %w", err)
	}

	if existingResponseCount > 0 {
		duration := time.Since(start)
		slog.Warn("Student already submitted responses for this test",
			"student_id", studentID,
			"test_id", testResponsesInput.TestID,
			"existing_count", existingResponseCount,
			"duration_ms", duration.Milliseconds(),
		)
		return nil, fmt.Errorf("student has already submitted responses for test ID %d", testResponsesInput.TestID)
	}

	// Create a map of question ID to question for quick lookup
	questionMap := make(map[uint]models.Question)
	for _, section := range test.Sections {
		for _, question := range section.Questions {
			questionMap[question.ID] = question
		}
	}

	// Validate that all questions in the responses belong to this test
	for _, responseInput := range testResponsesInput.Responses {
		if _, exists := questionMap[responseInput.QuestionID]; !exists {
			return nil, fmt.Errorf("question with ID %d is not part of test %d", responseInput.QuestionID, testResponsesInput.TestID)
		}
	}

	// Now start transaction for actual write operations
	tx := p.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// Process each response - just record without evaluation
	var responseResults []models.TestResponseResult

	for _, responseInput := range testResponsesInput.Responses {
		// Create the test response record without evaluation
		testResponse := models.TestResponse{
			StudentID:         studentID,
			TestID:            testResponsesInput.TestID,
			QuestionID:        responseInput.QuestionID,
			SelectedOptionIDs: responseInput.SelectedOptionIDs,
			ResponseText:      responseInput.ResponseText,
			// Leave IsCorrect as false (default) and CalculatedScore as nil
			// These will be set during evaluation
		}

		// Save the response
		if err := tx.Create(&testResponse).Error; err != nil {
			tx.Rollback()
			duration := time.Since(start)
			slog.Error("Failed to save test response",
				"student_id", studentID,
				"test_id", testResponsesInput.TestID,
				"question_id", responseInput.QuestionID,
				"error", err.Error(),
				"duration_ms", duration.Milliseconds(),
			)
			return nil, fmt.Errorf("failed to save response for question %d: %w", responseInput.QuestionID, err)
		}

		// Add to results - without evaluation data
		responseResult := models.TestResponseResult{
			QuestionID: responseInput.QuestionID,
			Message:    "Response recorded successfully - pending evaluation",
		}

		responseResults = append(responseResults, responseResult)
	}

	// Commit the transaction
	if err := tx.Commit().Error; err != nil {
		duration := time.Since(start)
		slog.Error("Failed to commit test responses transaction",
			"student_id", studentID,
			"test_id", testResponsesInput.TestID,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return nil, fmt.Errorf("failed to commit responses: %w", err)
	}

	// Update test evaluation status to Pending since new responses were added
	if err := p.UpdateTestEvaluationStatus(ctx, testResponsesInput.TestID, models.EvaluationStatusPending); err != nil {
		// Log the error but don't fail the entire operation
		slog.Warn("Failed to update test evaluation status after recording responses",
			"test_id", testResponsesInput.TestID,
			"error", err.Error(),
		)
	}

	duration := time.Since(start)
	slog.Info("Test responses recorded successfully",
		"student_id", studentID,
		"test_id", testResponsesInput.TestID,
		"total_questions", len(testResponsesInput.Responses),
		"duration_ms", duration.Milliseconds(),
	)

	// Prepare the result - without evaluation data
	result := &models.TestResponsesResult{
		TestID:          testResponsesInput.TestID,
		StudentID:       studentID,
		TotalQuestions:  len(testResponsesInput.Responses),
		CorrectAnswers:  0, // Will be set after evaluation
		TotalScore:      0, // Will be set after evaluation
		ResponseResults: responseResults,
		Message:         fmt.Sprintf("Successfully recorded %d responses. Responses are pending evaluation.", len(testResponsesInput.Responses)),
	}

	return result, nil
}

// GetStudentTestResponses retrieves all responses for a specific student and test with enhanced information
func (p *DbPlugin) GetStudentTestResponses(ctx context.Context, studentID, testID uint) (*models.StudentTestResponsesResult, error) {
	start := time.Now()

	slog.Debug("Getting enhanced student test responses",
		"student_id", studentID,
		"test_id", testID,
	)

	// Get the test information
	var test models.Test
	if err := p.db.First(&test, testID).Error; err != nil {
		duration := time.Since(start)
		slog.Error("Failed to retrieve test information",
			"student_id", studentID,
			"test_id", testID,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return nil, fmt.Errorf("failed to retrieve test information: %w", err)
	}

	// Get student information
	var student models.Student
	if err := p.db.Preload("User").First(&student, studentID).Error; err != nil {
		duration := time.Since(start)
		slog.Error("Failed to retrieve student information",
			"student_id", studentID,
			"test_id", testID,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return nil, fmt.Errorf("failed to retrieve student information: %w", err)
	}

	// Get all responses for the student and test
	var responses []models.TestResponse
	if err := p.db.Preload("Question").Preload("Student.User").Preload("Test").
		Where("student_id = ? AND test_id = ?", studentID, testID).
		Find(&responses).Error; err != nil {
		duration := time.Since(start)
		slog.Error("Failed to retrieve student test responses",
			"student_id", studentID,
			"test_id", testID,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return nil, fmt.Errorf("failed to retrieve responses: %w", err)
	}

	if len(responses) == 0 {
		duration := time.Since(start)
		slog.Info("No responses found for student and test",
			"student_id", studentID,
			"test_id", testID,
			"duration_ms", duration.Milliseconds(),
		)
		return &models.StudentTestResponsesResult{
			TestID:              testID,
			TestName:            test.Name,
			StudentID:           studentID,
			StudentName:         student.User.FullName,
			TotalScore:          0,
			ResponsesRecordedAt: time.Time{}, // Zero time if no responses
			Responses:           []models.TestResponse{},
			Message:             "No responses found for this test",
		}, nil
	}

	// Find the earliest recorded time (when responses were first submitted)
	var earliestRecordedAt time.Time = responses[0].CreatedAt
	for _, response := range responses {
		if response.CreatedAt.Before(earliestRecordedAt) {
			earliestRecordedAt = response.CreatedAt
		}
	}

	// Get total score from StudentTestMark table if available
	var totalScore int
	var studentTestMark models.StudentTestMark
	if err := p.db.Where("student_id = ? AND test_id = ?", studentID, testID).First(&studentTestMark).Error; err == nil {
		// Use the final marks from StudentTestMark table
		totalScore = studentTestMark.FinalMarks
		slog.Debug("Using total score from StudentTestMark table",
			"student_id", studentID,
			"test_id", testID,
			"final_marks", studentTestMark.FinalMarks,
		)
	} else {
		// StudentTestMark not found, return 0 score
		totalScore = 0
		slog.Debug("StudentTestMark not found, returning zero score",
			"student_id", studentID,
			"test_id", testID,
		)
	}

	duration := time.Since(start)
	slog.Debug("Enhanced student test responses retrieved successfully",
		"student_id", studentID,
		"test_id", testID,
		"response_count", len(responses),
		"total_score", totalScore,
		"recorded_at", earliestRecordedAt,
		"duration_ms", duration.Milliseconds(),
	)

	result := &models.StudentTestResponsesResult{
		TestID:              testID,
		TestName:            test.Name,
		StudentID:           studentID,
		StudentName:         student.User.FullName,
		TotalScore:          totalScore,
		ResponsesRecordedAt: earliestRecordedAt,
		Responses:           responses,
		Message:             fmt.Sprintf("Retrieved %d responses with total score: %d", len(responses), totalScore),
	}

	return result, nil
}

// EvaluateTestResponses evaluates all unevaluated responses for a given test
func (p *DbPlugin) EvaluateTestResponses(ctx context.Context, testID uint) (*models.TestEvaluationResult, error) {
	start := time.Now()

	slog.Info("Starting test response evaluation",
		"test_id", testID,
	)

	// Verify the test exists and get test details (read-only operation, no transaction needed)
	var test models.Test
	if err := p.db.Preload("Sections.Questions.Options").First(&test, testID).Error; err != nil {
		duration := time.Since(start)
		slog.Error("Test not found for evaluation",
			"test_id", testID,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return nil, fmt.Errorf("test with ID %d not found: %w", testID, err)
	}

	// Get all unevaluated responses for this test (read-only operation, no transaction needed)
	var unevaluatedResponses []models.TestResponse
	if err := p.db.Preload("Student.User").Preload("Question.Options").
		Where("test_id = ? AND calculated_score IS NULL", testID).
		Find(&unevaluatedResponses).Error; err != nil {
		duration := time.Since(start)
		slog.Error("Failed to retrieve unevaluated responses",
			"test_id", testID,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return nil, fmt.Errorf("failed to retrieve unevaluated responses: %w", err)
	}

	if len(unevaluatedResponses) == 0 {
		duration := time.Since(start)
		slog.Info("No unevaluated responses found for test",
			"test_id", testID,
			"duration_ms", duration.Milliseconds(),
		)
		return &models.TestEvaluationResult{
			TestID:                 testID,
			TestName:               test.Name,
			TotalStudentsEvaluated: 0,
			StudentResults:         []models.StudentEvaluationResult{},
			Message:                "No unevaluated responses found for this test",
		}, nil
	}

	// Group responses by student (in-memory operation, no transaction needed)
	studentResponsesMap := make(map[uint][]models.TestResponse)
	for _, response := range unevaluatedResponses {
		studentResponsesMap[response.StudentID] = append(studentResponsesMap[response.StudentID], response)
	}

	// Now start transaction for actual write operations (evaluation and updates)
	tx := p.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	var studentResults []models.StudentEvaluationResult
	evaluatedCount := 0

	// Evaluate responses for each student
	for studentID, responses := range studentResponsesMap {
		studentResult, err := p.evaluateStudentResponses(tx, studentID, responses)
		if err != nil {
			tx.Rollback()
			duration := time.Since(start)
			slog.Error("Failed to evaluate student responses",
				"test_id", testID,
				"student_id", studentID,
				"error", err.Error(),
				"duration_ms", duration.Milliseconds(),
			)
			return nil, fmt.Errorf("failed to evaluate responses for student %d: %w", studentID, err)
		}

		studentResults = append(studentResults, *studentResult)
		evaluatedCount++
	}

	// Commit the transaction
	if err := tx.Commit().Error; err != nil {
		duration := time.Since(start)
		slog.Error("Failed to commit evaluation transaction",
			"test_id", testID,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return nil, fmt.Errorf("failed to commit evaluation: %w", err)
	}

	// Determine and update the test evaluation status after evaluation
	newStatus, err := p.DetermineTestEvaluationStatus(ctx, testID)
	if err != nil {
		// Log the error but don't fail the entire operation
		slog.Warn("Failed to determine test evaluation status after evaluation",
			"test_id", testID,
			"error", err.Error(),
		)
	} else {
		if err := p.UpdateTestEvaluationStatus(ctx, testID, newStatus); err != nil {
			// Log the error but don't fail the entire operation
			slog.Warn("Failed to update test evaluation status after evaluation",
				"test_id", testID,
				"new_status", newStatus,
				"error", err.Error(),
			)
		}
	}

	duration := time.Since(start)
	slog.Info("Test response evaluation completed successfully",
		"test_id", testID,
		"students_evaluated", evaluatedCount,
		"total_responses_evaluated", len(unevaluatedResponses),
		"duration_ms", duration.Milliseconds(),
	)

	// Prepare the result
	result := &models.TestEvaluationResult{
		TestID:                 testID,
		TestName:               test.Name,
		TotalStudentsEvaluated: evaluatedCount,
		StudentResults:         studentResults,
		Message:                fmt.Sprintf("Successfully evaluated responses for %d students (%d total responses)", evaluatedCount, len(unevaluatedResponses)),
	}

	return result, nil
}

// evaluateStudentResponses evaluates all responses for a single student
func (p *DbPlugin) evaluateStudentResponses(tx *gorm.DB, studentID uint, responses []models.TestResponse) (*models.StudentEvaluationResult, error) {
	var totalPositiveMarks int
	var totalNegativeMarks int
	var correctAnswers int
	studentName := ""
	testID := uint(0)

	// Get student name and test ID from the first response
	if len(responses) > 0 {
		studentName = responses[0].Student.User.FullName
		testID = responses[0].TestID
	}

	// Group responses by section to apply section-specific scoring
	sectionResponsesMap := make(map[uint][]models.TestResponse)
	for _, response := range responses {
		// Find which section this question belongs to
		var section models.Section
		if err := tx.Preload("SectionType").
			Joins("JOIN sections_questions ON sections.id = sections_questions.section_id").
			Where("sections_questions.question_id = ? AND sections.test_id = ?", response.QuestionID, response.TestID).
			First(&section).Error; err != nil {
			slog.Warn("Could not find section for question, using default scoring",
				"question_id", response.QuestionID,
				"test_id", response.TestID,
				"error", err.Error(),
			)
			// Use default scoring if section not found
			sectionResponsesMap[0] = append(sectionResponsesMap[0], response)
		} else {
			sectionResponsesMap[section.ID] = append(sectionResponsesMap[section.ID], response)
		}
	}

	// Evaluate responses for each section
	for sectionID, sectionResponses := range sectionResponsesMap {
		positiveMarks := 1.0 // Default positive marks
		negativeMarks := 0.0 // Default negative marks

		// Get section type for scoring if section found
		if sectionID != 0 {
			var section models.Section
			if err := tx.Preload("SectionType").First(&section, sectionID).Error; err == nil {
				positiveMarks = section.SectionType.PositiveMarks
				negativeMarks = section.SectionType.NegativeMarks
			}
		}

		// Evaluate each response in this section
		for i := range sectionResponses {
			response := &sectionResponses[i]

			// Evaluate the response
			isCorrect, _ := p.evaluateResponse(&response.Question, response)

			// Calculate marks based on section type
			var calculatedScore int
			if isCorrect {
				calculatedScore = int(positiveMarks)
				totalPositiveMarks += calculatedScore
				correctAnswers++
			} else {
				// Apply negative marking only if response was attempted
				if len(response.SelectedOptionIDs) > 0 || (response.ResponseText != nil && *response.ResponseText != "") {
					calculatedScore = -int(negativeMarks)
					totalNegativeMarks += int(negativeMarks)
				} else {
					calculatedScore = 0 // No marks for unattempted questions
				}
			}

			// Update the response with evaluation results
			response.IsCorrect = isCorrect
			response.CalculatedScore = &calculatedScore

			// Save the updated response
			if err := tx.Save(response).Error; err != nil {
				return nil, fmt.Errorf("failed to update response for question %d: %w", response.QuestionID, err)
			}
		}
	}

	// Create or update StudentTestMark record
	if testID > 0 {
		studentTestMark := models.StudentTestMark{
			StudentID:          int(studentID),
			TestID:             int(testID),
			TotalPositiveMarks: totalPositiveMarks,
			TotalNegativeMarks: totalNegativeMarks,
		}

		// Use GORM's Save method which will create or update based on primary key
		if err := tx.Save(&studentTestMark).Error; err != nil {
			slog.Error("Failed to create/update StudentTestMark",
				"student_id", studentID,
				"test_id", testID,
				"positive_marks", totalPositiveMarks,
				"negative_marks", totalNegativeMarks,
				"error", err.Error(),
			)
			return nil, fmt.Errorf("failed to create/update student test marks: %w", err)
		}

		slog.Info("StudentTestMark created/updated successfully",
			"student_id", studentID,
			"test_id", testID,
			"positive_marks", totalPositiveMarks,
			"negative_marks", totalNegativeMarks,
			"final_marks", totalPositiveMarks-totalNegativeMarks,
		)
	}

	finalScore := totalPositiveMarks - totalNegativeMarks

	// Create the student evaluation result
	result := &models.StudentEvaluationResult{
		StudentID:      studentID,
		StudentName:    studentName,
		TotalQuestions: len(responses),
		CorrectAnswers: correctAnswers,
		TotalScore:     finalScore,
		EvaluationTime: time.Now().Format(time.RFC3339),
		Message:        fmt.Sprintf("Evaluated %d responses: %d correct, final score: %d (positive: %d, negative: %d)", len(responses), correctAnswers, finalScore, totalPositiveMarks, totalNegativeMarks),
	}

	return result, nil
}

// GetTestRankings retrieves rankings for all students in a specific test
func (p *DbPlugin) GetTestRankings(ctx context.Context, testID uint, limit, offset int) (*models.TestRankingResult, error) {
	start := time.Now()

	slog.Info("Getting test rankings",
		"test_id", testID,
		"limit", limit,
		"offset", offset,
	)

	// Verify the test exists and get test details (read-only operation)
	var test models.Test
	if err := p.db.First(&test, testID).Error; err != nil {
		duration := time.Since(start)
		slog.Error("Test not found for rankings",
			"test_id", testID,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return nil, fmt.Errorf("test with ID %d not found: %w", testID, err)
	}

	// Get all student marks for this test, ordered by final marks descending
	var studentMarks []models.StudentTestMark
	query := p.db.Preload("Student.User").
		Where("test_id = ?", testID).
		Order("final_marks DESC, student_id ASC") // Secondary sort by student_id for consistent ordering

	// Apply pagination if specified
	if limit > 0 {
		query = query.Limit(limit)
	}
	if offset > 0 {
		query = query.Offset(offset)
	}

	if err := query.Find(&studentMarks).Error; err != nil {
		duration := time.Since(start)
		slog.Error("Failed to retrieve student marks for rankings",
			"test_id", testID,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return nil, fmt.Errorf("failed to retrieve student marks: %w", err)
	}

	if len(studentMarks) == 0 {
		duration := time.Since(start)
		slog.Info("No student marks found for test",
			"test_id", testID,
			"duration_ms", duration.Milliseconds(),
		)
		return &models.TestRankingResult{
			TestID:          testID,
			TestName:        test.Name,
			TotalStudents:   0,
			SubjectScores:   make(map[string]models.SubjectScoreInfo),
			StudentRankings: []models.StudentRankingInfo{},
			Message:         "No student marks found for this test",
		}, nil
	}

	// Calculate statistics
	var totalMarks int
	highestMarks := studentMarks[0].FinalMarks
	lowestMarks := studentMarks[len(studentMarks)-1].FinalMarks

	for _, mark := range studentMarks {
		totalMarks += mark.FinalMarks
		if mark.FinalMarks > highestMarks {
			highestMarks = mark.FinalMarks
		}
		if mark.FinalMarks < lowestMarks {
			lowestMarks = mark.FinalMarks
		}
	}

	averageMarks := float64(totalMarks) / float64(len(studentMarks))

	// Get total count for percentile calculation (if pagination is used)
	var totalStudents int64
	if err := p.db.Model(&models.StudentTestMark{}).
		Where("test_id = ?", testID).
		Count(&totalStudents).Error; err != nil {
		return nil, fmt.Errorf("failed to count total students: %w", err)
	}

	// Calculate subject-wise and topic-wise average scores
	subjectScores, err := p.calculateSubjectAndTopicScores(ctx, testID)
	if err != nil {
		slog.Error("Failed to calculate subject and topic scores",
			"test_id", testID,
			"error", err.Error(),
		)
		// Continue without subject scores rather than failing the entire request
		subjectScores = make(map[string]models.SubjectScoreInfo)
	}

	// Build student rankings with rank and percentile
	var studentRankings []models.StudentRankingInfo
	currentRank := offset + 1 // Start rank based on offset

	for i, mark := range studentMarks {
		// Handle tied scores - students with same marks get same rank
		if i > 0 && mark.FinalMarks < studentMarks[i-1].FinalMarks {
			currentRank = offset + i + 1
		}

		// Calculate percentile (percentage of students with lower scores)
		percentile := float64(int(totalStudents)-currentRank+1) / float64(totalStudents) * 100

		studentRanking := models.StudentRankingInfo{
			StudentID:          uint(mark.StudentID),
			StudentName:        mark.Student.User.FullName,
			StudentEmail:       mark.Student.User.Email,
			TotalPositiveMarks: mark.TotalPositiveMarks,
			TotalNegativeMarks: mark.TotalNegativeMarks,
			FinalMarks:         mark.FinalMarks,
			Rank:               currentRank,
			Percentile:         percentile,
		}

		studentRankings = append(studentRankings, studentRanking)
	}

	duration := time.Since(start)
	slog.Info("Test rankings retrieved successfully",
		"test_id", testID,
		"total_students", totalStudents,
		"returned_count", len(studentRankings),
		"highest_marks", highestMarks,
		"lowest_marks", lowestMarks,
		"average_marks", averageMarks,
		"duration_ms", duration.Milliseconds(),
	)

	// Prepare the result
	result := &models.TestRankingResult{
		TestID:          testID,
		TestName:        test.Name,
		TotalStudents:   int(totalStudents),
		HighestMarks:    highestMarks,
		LowestMarks:     lowestMarks,
		AverageMarks:    averageMarks,
		SubjectScores:   subjectScores,
		StudentRankings: studentRankings,
		Message:         fmt.Sprintf("Retrieved rankings for %d students (showing %d)", totalStudents, len(studentRankings)),
	}

	return result, nil
}

// evaluateResponse evaluates a student's response and calculates the score
func (p *DbPlugin) evaluateResponse(question *models.Question, response *models.TestResponse) (bool, int) {
	// For multiple choice questions, check selected options
	if len(response.SelectedOptionIDs) > 0 {
		return p.evaluateMultipleChoiceResponse(question, response)
	}

	// For text-based questions, compare with correct answer
	if response.ResponseText != nil {
		return p.evaluateTextResponse(question, response)
	}

	// No response provided
	return false, 0
}

// evaluateMultipleChoiceResponse evaluates multiple choice responses
func (p *DbPlugin) evaluateMultipleChoiceResponse(question *models.Question, response *models.TestResponse) (bool, int) {
	// Load question options if not already loaded
	if len(question.Options) == 0 {
		if err := p.db.Where("question_id = ?", question.ID).Find(&question.Options).Error; err != nil {
			slog.Error("Failed to load question options for evaluation",
				"question_id", question.ID,
				"error", err.Error(),
			)
			return false, 0
		}
	}

	// Get correct option IDs
	var correctOptionIDs []int
	for _, option := range question.Options {
		if option.IsCorrect {
			correctOptionIDs = append(correctOptionIDs, int(option.ID))
		}
	}

	// Compare selected options with correct options
	if len(correctOptionIDs) == 0 {
		slog.Warn("No correct options found for question", "question_id", question.ID)
		return false, 0
	}

	// Check if selected options match correct options exactly
	if len(response.SelectedOptionIDs) != len(correctOptionIDs) {
		return false, 0
	}

	// Create maps for comparison
	selectedMap := make(map[int]bool)
	for _, id := range response.SelectedOptionIDs {
		selectedMap[id] = true
	}

	correctMap := make(map[int]bool)
	for _, id := range correctOptionIDs {
		correctMap[id] = true
	}

	// Check if all selected options are correct and all correct options are selected
	for _, id := range response.SelectedOptionIDs {
		if !correctMap[id] {
			return false, 0
		}
	}

	for _, id := range correctOptionIDs {
		if !selectedMap[id] {
			return false, 0
		}
	}

	// All correct - return positive score (could be configurable based on question difficulty)
	return true, 1
}

// evaluateTextResponse evaluates text-based responses
func (p *DbPlugin) evaluateTextResponse(question *models.Question, response *models.TestResponse) (bool, int) {
	if response.ResponseText == nil || question.CorrectAnswer == "" {
		return false, 0
	}

	// Simple string comparison (case-insensitive)
	// In a real system, you might want more sophisticated text matching
	responseText := *response.ResponseText
	if len(responseText) == 0 {
		return false, 0
	}

	// For now, do exact match (case-insensitive)
	// You could enhance this with fuzzy matching, keyword matching, etc.
	isCorrect := responseText == question.CorrectAnswer

	if isCorrect {
		return true, 1
	}

	return false, 0
}

// calculateSubjectAndTopicScores calculates average scores per subject and topic for a test
func (p *DbPlugin) calculateSubjectAndTopicScores(ctx context.Context, testID uint) (map[string]models.SubjectScoreInfo, error) {
	slog.Debug("Calculating subject and topic scores", "test_id", testID)

	// Get all test responses with question, topic, chapter, and subject information
	var responses []models.TestResponse
	err := p.db.Preload("Question.Topic.Chapter.Subject").
		Where("test_id = ? AND calculated_score IS NOT NULL", testID).
		Find(&responses).Error
	if err != nil {
		return nil, fmt.Errorf("failed to fetch test responses with question details: %w", err)
	}

	if len(responses) == 0 {
		slog.Debug("No evaluated responses found for test", "test_id", testID)
		return make(map[string]models.SubjectScoreInfo), nil
	}

	// Maps to track scores and counts for subjects and topics
	subjectScores := make(map[string][]int)      // subject_name -> []scores
	topicScores := make(map[string][]int)        // topic_name -> []scores
	subjectToTopics := make(map[string][]string) // subject_name -> []topic_names

	// Process each response
	for _, response := range responses {
		if response.CalculatedScore == nil {
			continue
		}

		score := *response.CalculatedScore
		subjectName := response.Question.Topic.Chapter.Subject.Name
		topicName := response.Question.Topic.Name

		// Add score to subject
		subjectScores[subjectName] = append(subjectScores[subjectName], score)

		// Add score to topic
		topicScores[topicName] = append(topicScores[topicName], score)

		// Track which topics belong to which subjects
		if topics, exists := subjectToTopics[subjectName]; exists {
			// Check if topic already exists in the slice
			found := false
			for _, existingTopic := range topics {
				if existingTopic == topicName {
					found = true
					break
				}
			}
			if !found {
				subjectToTopics[subjectName] = append(subjectToTopics[subjectName], topicName)
			}
		} else {
			subjectToTopics[subjectName] = []string{topicName}
		}
	}

	// Calculate averages and build result
	result := make(map[string]models.SubjectScoreInfo)

	for subjectName, scores := range subjectScores {
		if len(scores) == 0 {
			continue
		}

		// Calculate subject average
		var total int
		for _, score := range scores {
			total += score
		}
		subjectAverage := float64(total) / float64(len(scores))

		// Calculate topic averages for this subject
		topicAverages := make(map[string]float64)
		if topics, exists := subjectToTopics[subjectName]; exists {
			for _, topicName := range topics {
				if topicScoresList, topicExists := topicScores[topicName]; topicExists && len(topicScoresList) > 0 {
					var topicTotal int
					for _, score := range topicScoresList {
						topicTotal += score
					}
					topicAverages[topicName] = float64(topicTotal) / float64(len(topicScoresList))
				}
			}
		}

		result[subjectName] = models.SubjectScoreInfo{
			AverageScore: subjectAverage,
			TopicScores:  topicAverages,
		}
	}

	slog.Debug("Subject and topic scores calculated",
		"test_id", testID,
		"subjects_count", len(result),
		"total_responses", len(responses),
	)

	return result, nil
}
