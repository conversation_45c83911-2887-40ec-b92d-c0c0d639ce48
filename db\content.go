package db

import (
	"context"
	"fmt"
	"log/slog"
	"time"
	"ziaacademy-backend/internal/models"
)

// GetContentBySubjectsWithProgress retrieves content organized by subjects with progress information for a specific student
func (p *DbPlugin) GetContentBySubjectsWithProgress(ctx context.Context, studentID uint, courseID *uint, isStudent bool) (*models.ContentResponseWithProgress, error) {
	start := time.Now()
	slog.Info("Retrieving content organized by subjects with progress",
		"student_id", studentID,
		"course_id", courseID,
	)

	// First, get all subjects or filter by specific course
	var subjects []models.Subject
	subjectQuery := p.db.Order("name ASC")
	if courseID != nil {
		// Filter subjects by course using the many-to-many relationship
		subjectQuery = subjectQuery.Joins("JOIN courses_subjects ON subjects.id = courses_subjects.subject_id").
			Where("courses_subjects.course_id = ?", *courseID)
		slog.Debug("Filtering by specific course", "course_id", *courseID)
	}

	if err := subjectQuery.Find(&subjects).Error; err != nil {
		duration := time.Since(start)
		slog.Error("Failed to retrieve subjects",
			"student_id", studentID,
			"course_id", courseID,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return nil, fmt.Errorf("failed to retrieve subjects: %w", err)
	}

	// Get all video progress for this student in one query for efficiency
	var allProgress []models.VideoProgress
	if err := p.db.Where("student_id = ?", studentID).Find(&allProgress).Error; err != nil {
		slog.Warn("Failed to retrieve video progress for student",
			"student_id", studentID,
			"error", err.Error(),
		)
	}

	// Get all study material progress for this student in one query for efficiency
	var allMaterialProgress []models.StudyMaterialProgress
	if err := p.db.Where("student_id = ?", studentID).Find(&allMaterialProgress).Error; err != nil {
		slog.Warn("Failed to retrieve study material progress for student",
			"student_id", studentID,
			"error", err.Error(),
		)
	}

	// Create a map for quick progress lookup
	progressMap := make(map[uint]models.VideoProgress)
	for _, progress := range allProgress {
		progressMap[progress.VideoID] = progress
	}

	// Create a map for quick study material progress lookup
	materialProgressMap := make(map[uint]models.StudyMaterialProgress)
	for _, progress := range allMaterialProgress {
		materialProgressMap[progress.StudyMaterialID] = progress
	}

	contentBySubjects := make(map[string]models.SubjectContentWithProgress)
	totalVideos := 0
	totalPdfs := 0

	for _, subject := range subjects {
		// Get videos for this subject
		var videos []models.Video
		if err := p.db.Joins("JOIN chapters ON videos.chapter_id = chapters.id").
			Where("chapters.subject_id = ?", subject.ID).
			Order("videos.updated_at DESC").
			Find(&videos).Error; err != nil {
			slog.Error("Failed to retrieve videos for subject",
				"subject_id", subject.ID,
				"subject_name", subject.Name,
				"error", err.Error(),
			)
			continue
		}

		// Get study materials for this subject
		var studyMaterials []models.StudyMaterial
		if err := p.db.Joins("JOIN chapters ON study_materials.chapter_id = chapters.id").
			Where("chapters.subject_id = ?", subject.ID).
			Order("study_materials.updated_at DESC").
			Find(&studyMaterials).Error; err != nil {
			slog.Error("Failed to retrieve study materials for subject",
				"subject_id", subject.ID,
				"subject_name", subject.Name,
				"error", err.Error(),
			)
			continue
		}

		// Convert videos to response structures with progress
		var videosWithProgress []models.VideoForGet
		for _, video := range videos {
			videoWithProgress := models.VideoForGet{
				Name:        video.Name,
				DisplayName: video.DisplayName,
				VideoUrl:    video.VideoUrl,
				ViewCount:   video.ViewCount,
				ChapterID:   video.ChapterID,
				UpdatedAt:   video.UpdatedAt,
			}

			// Add progress information if available
			if progress, exists := progressMap[video.ID]; exists {
				videoWithProgress.ProgressSeconds = progress.ProgressSeconds
				videoWithProgress.ProgressPercent = progress.ProgressPercent
				videoWithProgress.VideoDuration = progress.VideoDuration
				videoWithProgress.IsCompleted = progress.IsCompleted
				videoWithProgress.LastWatchedAt = &progress.LastWatchedAt
			}

			videosWithProgress = append(videosWithProgress, videoWithProgress)
		}

		// Convert study materials to response structures with progress
		var materialsForGet []models.StudyMaterialForGet
		for _, material := range studyMaterials {
			materialForGet := models.StudyMaterialForGet{
				Name:         material.Name,
				DisplayName:  material.DisplayName,
				Url:          material.Url,
				TileImageUrl: material.TileImageUrl,
				ChapterID:    material.ChapterID,
				UpdatedAt:    material.UpdatedAt,
			}

			// Add progress information if available
			if progress, exists := materialProgressMap[material.ID]; exists {
				materialForGet.LastReadAt = &progress.LastReadAt
			}

			materialsForGet = append(materialsForGet, materialForGet)
		}

		// Only include subjects that have content
		if len(videosWithProgress) > 0 || len(materialsForGet) > 0 {
			contentBySubjects[subject.Name] = models.SubjectContentWithProgress{
				SubjectID:   subject.ID,
				DisplayName: subject.DisplayName,
				Videos:      videosWithProgress,
				Pdfs:        materialsForGet,
				VideoCount:  len(videosWithProgress),
				PdfCount:    len(materialsForGet),
			}

			totalVideos += len(videosWithProgress)
			totalPdfs += len(materialsForGet)
		}
	}

	// Get recently accessed content organized by subjects (only for students)
	recentlyAccessedContent := make(map[string]models.RecentlyAccessedContent)

	if isStudent {
		// Create a map to organize content by subjects
		subjectContentMap := make(map[uint]*models.RecentlyAccessedBySubject)

		// Get recently watched videos with subject information using joins
		type VideoWithSubject struct {
			models.VideoProgress
			Video       models.Video `gorm:"embedded;embeddedPrefix:video_"`
			SubjectID   uint         `gorm:"column:subject_id"`
			SubjectName string       `gorm:"column:subject_name"`
			DisplayName string       `gorm:"column:subject_display_name"`
		}

		var recentVideosWithSubject []VideoWithSubject
		if err := p.db.Table("video_progresses").
			Select("video_progresses.*, videos.name as video_name, videos.display_name as video_display_name, videos.video_url as video_video_url, videos.view_count as video_view_count, videos.chapter_id as video_chapter_id, videos.updated_at as video_updated_at, subjects.id as subject_id, subjects.name as subject_name, subjects.display_name as subject_display_name").
			Joins("JOIN videos ON video_progresses.video_id = videos.id").
			Joins("JOIN chapters ON videos.chapter_id = chapters.id").
			Joins("JOIN subjects ON chapters.subject_id = subjects.id").
			Where("video_progresses.student_id = ?", studentID).
			Order("video_progresses.last_watched_at DESC").
			Limit(20).
			Find(&recentVideosWithSubject).Error; err != nil {
			slog.Warn("Failed to retrieve recently watched videos with subject info",
				"student_id", studentID,
				"error", err.Error(),
			)
		}

		// Process recently watched videos
		for _, item := range recentVideosWithSubject {
			if _, exists := subjectContentMap[item.SubjectID]; !exists {
				subjectContentMap[item.SubjectID] = &models.RecentlyAccessedBySubject{
					SubjectID:   item.SubjectID,
					SubjectName: item.SubjectName,
					DisplayName: item.DisplayName,
					Videos:      []models.VideoForGet{},
					Pdfs:        []models.StudyMaterialForGet{},
				}
			}

			// Limit to 5 videos per subject
			if len(subjectContentMap[item.SubjectID].Videos) < 5 {
				recentVideo := models.VideoForGet{
					Name:            item.Video.Name,
					DisplayName:     item.Video.DisplayName,
					VideoUrl:        item.Video.VideoUrl,
					ViewCount:       item.Video.ViewCount,
					ChapterID:       item.Video.ChapterID,
					UpdatedAt:       item.Video.UpdatedAt,
					ProgressSeconds: item.ProgressSeconds,
					ProgressPercent: item.ProgressPercent,
					VideoDuration:   item.VideoDuration,
					IsCompleted:     item.IsCompleted,
					LastWatchedAt:   &item.LastWatchedAt,
				}
				subjectContentMap[item.SubjectID].Videos = append(subjectContentMap[item.SubjectID].Videos, recentVideo)
			}
		}

		// Get recently read PDFs with subject information using joins
		type MaterialWithSubject struct {
			models.StudyMaterialProgress
			StudyMaterial models.StudyMaterial `gorm:"embedded;embeddedPrefix:material_"`
			SubjectID     uint                 `gorm:"column:subject_id"`
			SubjectName   string               `gorm:"column:subject_name"`
			DisplayName   string               `gorm:"column:subject_display_name"`
		}

		var recentMaterialsWithSubject []MaterialWithSubject
		if err := p.db.Table("study_material_progresses").
			Select("study_material_progresses.*, study_materials.name as material_name, study_materials.display_name as material_display_name, study_materials.url as material_url, study_materials.tile_image_url as material_tile_image_url, study_materials.chapter_id as material_chapter_id, study_materials.updated_at as material_updated_at, subjects.id as subject_id, subjects.name as subject_name, subjects.display_name as subject_display_name").
			Joins("JOIN study_materials ON study_material_progresses.study_material_id = study_materials.id").
			Joins("JOIN chapters ON study_materials.chapter_id = chapters.id").
			Joins("JOIN subjects ON chapters.subject_id = subjects.id").
			Where("study_material_progresses.student_id = ?", studentID).
			Order("study_material_progresses.last_read_at DESC").
			Limit(20).
			Find(&recentMaterialsWithSubject).Error; err != nil {
			slog.Warn("Failed to retrieve recently read PDFs with subject info",
				"student_id", studentID,
				"error", err.Error(),
			)
		}

		// Process recently read PDFs
		for _, item := range recentMaterialsWithSubject {
			if _, exists := subjectContentMap[item.SubjectID]; !exists {
				subjectContentMap[item.SubjectID] = &models.RecentlyAccessedBySubject{
					SubjectID:   item.SubjectID,
					SubjectName: item.SubjectName,
					DisplayName: item.DisplayName,
					Videos:      []models.VideoForGet{},
					Pdfs:        []models.StudyMaterialForGet{},
				}
			}

			// Limit to 5 PDFs per subject
			if len(subjectContentMap[item.SubjectID].Pdfs) < 5 {
				recentPdf := models.StudyMaterialForGet{
					Name:         item.StudyMaterial.Name,
					DisplayName:  item.StudyMaterial.DisplayName,
					Url:          item.StudyMaterial.Url,
					TileImageUrl: item.StudyMaterial.TileImageUrl,
					ChapterID:    item.StudyMaterial.ChapterID,
					UpdatedAt:    item.StudyMaterial.UpdatedAt,
					LastReadAt:   &item.LastReadAt,
				}
				subjectContentMap[item.SubjectID].Pdfs = append(subjectContentMap[item.SubjectID].Pdfs, recentPdf)
			}
		}

		// Convert map to final map structure and only include subjects with content
		for _, subjectContent := range subjectContentMap {
			if len(subjectContent.Videos) > 0 || len(subjectContent.Pdfs) > 0 {
				recentlyAccessedContent[subjectContent.SubjectName] = models.RecentlyAccessedContent{
					SubjectID:   subjectContent.SubjectID,
					DisplayName: subjectContent.DisplayName,
					Videos:      subjectContent.Videos,
					Pdfs:        subjectContent.Pdfs,
				}
			}
		}
	}

	result := &models.ContentResponseWithProgress{
		Subjects: contentBySubjects,
		Summary: models.ContentSummary{
			TotalSubjects: len(contentBySubjects),
			TotalVideos:   totalVideos,
			TotalPdfs:     totalPdfs,
		},
		RecentlyAccessedContent: recentlyAccessedContent,
	}

	duration := time.Since(start)
	slog.Info("Content by subjects with progress retrieved successfully",
		"student_id", studentID,
		"course_id", courseID,
		"subject_count", len(contentBySubjects),
		"total_videos", totalVideos,
		"total_pdfs", totalPdfs,
		"duration_ms", duration.Milliseconds(),
	)

	return result, nil
}

func (p *DbPlugin) AddVideo(ctx context.Context, video *models.Video, chapterName string) (*models.Video, error) {
	start := time.Now()
	slog.Info("Adding video to chapter",
		"video_name", video.Name,
		"video_display_name", video.DisplayName,
		"chapter_name", chapterName,
	)

	// Find the chapter by name
	var chapter models.Chapter
	if err := p.db.Where("name = ?", chapterName).First(&chapter).Error; err != nil {
		duration := time.Since(start)
		slog.Error("Chapter not found for video addition",
			"video_name", video.Name,
			"chapter_name", chapterName,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return nil, fmt.Errorf("chapter '%s' not found: %w", chapterName, err)
	}

	// Set the chapter ID
	video.ChapterID = chapter.ID

	slog.Debug("Creating video with chapter association",
		"video_name", video.Name,
		"chapter_id", chapter.ID,
		"chapter_name", chapterName,
	)

	res := p.db.Create(video)
	duration := time.Since(start)

	if res.Error != nil {
		slog.Error("Failed to create video",
			"video_name", video.Name,
			"chapter_name", chapterName,
			"chapter_id", chapter.ID,
			"error", res.Error.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return nil, res.Error
	}

	slog.Info("Video added successfully",
		"video_id", video.ID,
		"video_name", video.Name,
		"video_display_name", video.DisplayName,
		"chapter_name", chapterName,
		"chapter_id", chapter.ID,
		"duration_ms", duration.Milliseconds(),
	)
	return video, nil
}

func (p *DbPlugin) AddStudyMaterial(ctx context.Context,
	pdf *models.StudyMaterial, chapterName string) (*models.StudyMaterial, error) {
	start := time.Now()
	slog.Info("Adding study material to chapter",
		"material_name", pdf.Name,
		"material_display_name", pdf.DisplayName,
		"chapter_name", chapterName,
	)

	// Find the chapter by name
	var chapter models.Chapter
	if err := p.db.Where("name = ?", chapterName).First(&chapter).Error; err != nil {
		duration := time.Since(start)
		slog.Error("Chapter not found for study material addition",
			"material_name", pdf.Name,
			"chapter_name", chapterName,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return nil, fmt.Errorf("chapter '%s' not found: %w", chapterName, err)
	}

	// Set the chapter ID
	pdf.ChapterID = chapter.ID

	slog.Debug("Creating study material with chapter association",
		"material_name", pdf.Name,
		"chapter_id", chapter.ID,
		"chapter_name", chapterName,
	)

	res := p.db.Create(pdf)
	duration := time.Since(start)

	if res.Error != nil {
		slog.Error("Failed to create study material",
			"material_name", pdf.Name,
			"chapter_name", chapterName,
			"chapter_id", chapter.ID,
			"error", res.Error.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return nil, res.Error
	}

	slog.Info("Study material added successfully",
		"material_id", pdf.ID,
		"material_name", pdf.Name,
		"material_display_name", pdf.DisplayName,
		"chapter_name", chapterName,
		"chapter_id", chapter.ID,
		"duration_ms", duration.Milliseconds(),
	)
	return pdf, nil
}
