package db

import (
	"context"
	"fmt"
	"log/slog"
	"strings"
	"time"
	"ziaacademy-backend/internal/models"
)

func (p *DbPlugin) CreateCourse(ctx context.Context, course *models.Course, subjectNames []string) (*models.Course, error) {
	start := time.Now()
	slog.Info("Creating course",
		"name", course.Name,
		"price", course.Price,
		"course_type", course.CourseType,
		"is_free", course.IsFree,
		"subject_count", len(subjectNames),
	)

	// Validate course type before starting transaction
	if course.CourseType != models.CourseTypeIITJEE && course.CourseType != models.CourseTypeNEET {
		return nil, fmt.Errorf("invalid course type '%s'. Must be either '%s' or '%s'",
			course.CourseType, models.CourseTypeIITJEE, models.CourseTypeNEET)
	}

	// Validate that all referenced subjects exist before starting transaction
	if len(subjectNames) > 0 {
		var existingSubjects []models.Subject
		if err := p.db.Where("name IN ?", subjectNames).Find(&existingSubjects).Error; err != nil {
			duration := time.Since(start)
			slog.Error("Failed to validate subjects for course creation",
				"course_name", course.Name,
				"subject_names", subjectNames,
				"error", err.Error(),
				"duration_ms", duration.Milliseconds(),
			)
			return nil, fmt.Errorf("failed to validate subjects: %w", err)
		}

		// Check if all subjects exist
		if len(existingSubjects) != len(subjectNames) {
			existingNames := make(map[string]bool)
			for _, subject := range existingSubjects {
				existingNames[subject.Name] = true
			}

			var missingSubjects []string
			for _, name := range subjectNames {
				if !existingNames[name] {
					missingSubjects = append(missingSubjects, name)
				}
			}

			duration := time.Since(start)
			slog.Error("Some subjects not found for course creation",
				"course_name", course.Name,
				"missing_subjects", missingSubjects,
				"duration_ms", duration.Milliseconds(),
			)
			return nil, fmt.Errorf("subjects not found: %v", missingSubjects)
		}
	}

	// Start a database transaction
	tx := p.db.Begin()
	if tx.Error != nil {
		duration := time.Since(start)
		slog.Error("Failed to begin transaction for course creation",
			"course_name", course.Name,
			"error", tx.Error.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return nil, tx.Error
	}

	defer func() {
		if r := recover(); r != nil {
			slog.Error("Panic during course creation",
				"course_name", course.Name,
				"panic", r,
			)
			tx.Rollback()
		}
	}()

	// Create the course
	if err := tx.Create(course).Error; err != nil {
		tx.Rollback()
		duration := time.Since(start)
		slog.Error("Failed to create course",
			"name", course.Name,
			"course_type", course.CourseType,
			"is_free", course.IsFree,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return nil, err
	}

	// Associate subjects with the course if any are provided
	if len(subjectNames) > 0 {
		var subjects []models.Subject
		if err := tx.Where("name IN ?", subjectNames).Find(&subjects).Error; err != nil {
			tx.Rollback()
			duration := time.Since(start)
			slog.Error("Failed to find subjects for course association",
				"course_name", course.Name,
				"subject_names", subjectNames,
				"error", err.Error(),
				"duration_ms", duration.Milliseconds(),
			)
			return nil, fmt.Errorf("failed to find subjects: %w", err)
		}

		// Associate subjects with the course
		if err := tx.Model(course).Association("Subjects").Append(subjects); err != nil {
			tx.Rollback()
			duration := time.Since(start)
			slog.Error("Failed to associate subjects with course",
				"course_name", course.Name,
				"subject_names", subjectNames,
				"error", err.Error(),
				"duration_ms", duration.Milliseconds(),
			)
			return nil, fmt.Errorf("failed to associate subjects with course: %w", err)
		}

		slog.Debug("Subjects associated with course",
			"course_name", course.Name,
			"course_id", course.ID,
			"subject_count", len(subjects),
		)
	}

	// Commit the transaction
	if err := tx.Commit().Error; err != nil {
		duration := time.Since(start)
		slog.Error("Failed to commit course creation transaction",
			"course_name", course.Name,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return nil, fmt.Errorf("failed to commit transaction: %w", err)
	}

	duration := time.Since(start)
	slog.Info("Course created successfully",
		"name", course.Name,
		"course_id", course.ID,
		"course_type", course.CourseType,
		"is_free", course.IsFree,
		"subject_count", len(subjectNames),
		"duration_ms", duration.Milliseconds(),
	)
	return course, nil
}

func (p *DbPlugin) GetCourses(ctx context.Context,
	userID uint) (*models.CoursesByCategory, error) {
	start := time.Now()
	slog.Debug("Retrieving courses for user", "user_id", userID)

	var allCourses []models.Course
	var coursesToReturn []models.CourseWithPurchased

	// Step 1: Get user information to check role
	var user models.User
	if err := p.db.Where("id = ?", userID).First(&user).Error; err != nil {
		duration := time.Since(start)
		slog.Error("Failed to find user",
			"user_id", userID,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return nil, err
	}

	slog.Debug("User found for GetCourses",
		"user_id", userID,
		"role", user.Role,
		"email", user.Email,
	)

	// Step 2: Query for all courses
	if err := p.db.Find(&allCourses).Error; err != nil {
		duration := time.Since(start)
		slog.Error("Failed to retrieve all courses",
			"user_id", userID,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return nil, err
	}

	// Step 3: Check if user is a student or not (case-insensitive)
	if strings.ToLower(user.Role) != "student" {
		// For non-student users (admin, etc.), return all courses without enrollment filtering
		slog.Info("Non-student user detected, returning all courses",
			"user_id", userID,
			"role", user.Role,
			"total_courses", len(allCourses),
		)

		for _, c := range allCourses {
			toAdd := models.CourseWithPurchased{
				ID:             c.ID,
				Name:           c.Name,
				Description:    c.Description,
				Price:          c.Price,
				Discount:       c.Discount,
				DurationInDays: c.DurationInDays,
				IsFree:         c.IsFree,
				CourseType:     c.CourseType,
				Purchased:      false, // Non-students don't have enrollment status
			}
			coursesToReturn = append(coursesToReturn, toAdd)
		}
	} else {
		// For student users, apply enrollment filtering
		var student models.Student
		if err := p.db.Preload("Courses").
			Where("user_id = ?", userID).
			First(&student).Error; err != nil {
			duration := time.Since(start)
			slog.Error("Failed to find student",
				"user_id", userID,
				"error", err.Error(),
				"duration_ms", duration.Milliseconds(),
			)
			return nil, err
		}

		// Create a map of enrolled course IDs for quick lookup
		enrolledCourseIDs := make(map[uint]bool)
		for _, enrolledCourse := range student.Courses {
			enrolledCourseIDs[enrolledCourse.ID] = true
		}

		slog.Debug("Retrieved student courses data",
			"user_id", userID,
			"total_courses", len(allCourses),
			"student_enrolled_courses", len(student.Courses),
		)

		// Filter courses based on requirements:
		// - Include all free courses
		// - Include only paid courses that the student has enrolled in
		for _, c := range allCourses {
			isEnrolled := enrolledCourseIDs[c.ID]

			// Include free courses (regardless of enrollment) or paid courses that student is enrolled in
			if c.IsFree || (!c.IsFree && isEnrolled) {
				toAdd := models.CourseWithPurchased{
					ID:             c.ID,
					Name:           c.Name,
					Description:    c.Description,
					Price:          c.Price,
					Discount:       c.Discount,
					DurationInDays: c.DurationInDays,
					IsFree:         c.IsFree,
					CourseType:     c.CourseType,
					Purchased:      isEnrolled,
				}
				coursesToReturn = append(coursesToReturn, toAdd)
			}
		}
	}

	// Step 4: Organize courses by free/paid status and course type
	result := organizeCoursesByCategory(coursesToReturn)

	duration := time.Since(start)
	slog.Info("Courses retrieved successfully",
		"user_id", userID,
		"user_role", user.Role,
		"total_courses", len(allCourses),
		"filtered_courses_returned", len(coursesToReturn),
		"free_courses_count", countFreeCourses(coursesToReturn),
		"enrolled_paid_courses_count", countEnrolledPaidCourses(coursesToReturn),
		"duration_ms", duration.Milliseconds(),
	)
	return result, nil
}

// countFreeCourses counts the number of free courses in the list
func countFreeCourses(courses []models.CourseWithPurchased) int {
	count := 0
	for _, course := range courses {
		if course.IsFree {
			count++
		}
	}
	return count
}

// countEnrolledPaidCourses counts the number of paid courses that the student is enrolled in
func countEnrolledPaidCourses(courses []models.CourseWithPurchased) int {
	count := 0
	for _, course := range courses {
		if !course.IsFree && course.Purchased {
			count++
		}
	}
	return count
}

// organizeCoursesByCategory organizes courses by free/paid status and then by course type
func organizeCoursesByCategory(courses []models.CourseWithPurchased) *models.CoursesByCategory {
	// Maps to group courses by free/paid and course type
	freeCoursesByType := make(map[string][]models.CourseWithPurchased)
	paidCoursesByType := make(map[string][]models.CourseWithPurchased)

	// Group courses
	for _, course := range courses {
		if course.IsFree {
			freeCoursesByType[course.CourseType] = append(freeCoursesByType[course.CourseType], course)
		} else {
			paidCoursesByType[course.CourseType] = append(paidCoursesByType[course.CourseType], course)
		}
	}

	// Convert maps to slices
	var freeCourses []models.CoursesByType
	var paidCourses []models.CoursesByType

	// Process free courses
	for courseType, courseList := range freeCoursesByType {
		freeCourses = append(freeCourses, models.CoursesByType{
			CourseType: courseType,
			Courses:    courseList,
		})
	}

	// Process paid courses
	for courseType, courseList := range paidCoursesByType {
		paidCourses = append(paidCourses, models.CoursesByType{
			CourseType: courseType,
			Courses:    courseList,
		})
	}

	return &models.CoursesByCategory{
		FreeCourses: freeCourses,
		PaidCourses: paidCourses,
	}
}

func (p *DbPlugin) GetCourseByID(ctx context.Context, courseID uint) (*models.CourseDetails, error) {
	start := time.Now()
	slog.Info("Retrieving course details by ID", "course_id", courseID)

	var course models.Course

	// Load course with all related data using nested preloading
	if err := p.db.Preload("Subjects.Chapters.Videos").
		Preload("Subjects.Chapters.StudyMaterials").
		First(&course, courseID).Error; err != nil {
		duration := time.Since(start)
		if err.Error() == "record not found" {
			slog.Warn("Course not found",
				"course_id", courseID,
				"duration_ms", duration.Milliseconds(),
			)
			return nil, fmt.Errorf("course with ID %d not found", courseID)
		}
		slog.Error("Failed to retrieve course by ID",
			"course_id", courseID,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return nil, fmt.Errorf("failed to retrieve course: %w", err)
	}

	// Transform the data into the response format
	var subjects []models.SubjectDetails
	for _, subject := range course.Subjects {
		var chapters []models.ChapterDetails
		for _, chapter := range subject.Chapters {
			// Transform videos
			var videos []models.VideoForGet
			for _, video := range chapter.Videos {
				videos = append(videos, models.VideoForGet{
					Name:        video.Name,
					DisplayName: video.DisplayName,
					VideoUrl:    video.VideoUrl,
					ViewCount:   video.ViewCount,
					ChapterID:   video.ChapterID,
					UpdatedAt:   video.UpdatedAt,
				})
			}

			// Transform study materials
			var studyMaterials []models.StudyMaterialForGet
			for _, material := range chapter.StudyMaterials {
				studyMaterials = append(studyMaterials, models.StudyMaterialForGet{
					Name:        material.Name,
					DisplayName: material.DisplayName,
					Url:         material.Url,
					ChapterID:   material.ChapterID,
					UpdatedAt:   material.UpdatedAt,
				})
			}

			chapters = append(chapters, models.ChapterDetails{
				ID:             chapter.ID,
				Name:           chapter.Name,
				DisplayName:    chapter.DisplayName,
				Videos:         videos,
				StudyMaterials: studyMaterials,
			})
		}

		subjects = append(subjects, models.SubjectDetails{
			ID:          subject.ID,
			Name:        subject.Name,
			DisplayName: subject.DisplayName,
			Chapters:    chapters,
		})
	}

	courseDetails := &models.CourseDetails{
		ID:             course.ID,
		Name:           course.Name,
		Description:    course.Description,
		Price:          course.Price,
		Discount:       course.Discount,
		DurationInDays: course.DurationInDays,
		IsFree:         course.IsFree,
		CourseType:     course.CourseType,
		Subjects:       subjects,
		CreatedAt:      course.CreatedAt,
		UpdatedAt:      course.UpdatedAt,
	}

	duration := time.Since(start)
	slog.Info("Course details retrieved successfully",
		"course_id", courseID,
		"course_name", course.Name,
		"subject_count", len(subjects),
		"duration_ms", duration.Milliseconds(),
	)

	return courseDetails, nil
}

func (p *DbPlugin) AssociateTestWithCourse(ctx context.Context, courseID, testID uint) error {
	start := time.Now()
	slog.Info("Associating test with course", "course_id", courseID, "test_id", testID)

	// Start a transaction
	tx := p.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			slog.Error("Panic during test-course association",
				"course_id", courseID,
				"test_id", testID,
				"panic", r,
			)
			tx.Rollback()
		}
	}()

	// Verify the course exists
	var course models.Course
	if err := tx.First(&course, courseID).Error; err != nil {
		tx.Rollback()
		duration := time.Since(start)
		if err.Error() == "record not found" {
			slog.Warn("Course not found for association",
				"course_id", courseID,
				"test_id", testID,
				"duration_ms", duration.Milliseconds(),
			)
			return fmt.Errorf("course with ID %d not found", courseID)
		}
		slog.Error("Failed to find course for association",
			"course_id", courseID,
			"test_id", testID,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return fmt.Errorf("failed to find course: %w", err)
	}

	// Verify the test exists
	var test models.Test
	if err := tx.First(&test, testID).Error; err != nil {
		tx.Rollback()
		duration := time.Since(start)
		if err.Error() == "record not found" {
			slog.Warn("Test not found for association",
				"course_id", courseID,
				"test_id", testID,
				"duration_ms", duration.Milliseconds(),
			)
			return fmt.Errorf("test with ID %d not found", testID)
		}
		slog.Error("Failed to find test for association",
			"course_id", courseID,
			"test_id", testID,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return fmt.Errorf("failed to find test: %w", err)
	}

	slog.Debug("Found course and test for association",
		"course_id", courseID,
		"course_name", course.Name,
		"test_id", testID,
		"test_name", test.Name,
	)

	// Load existing tests for this course
	var existingTests []models.Test
	if err := tx.Model(&course).Association("Tests").Find(&existingTests); err != nil {
		tx.Rollback()
		duration := time.Since(start)
		slog.Error("Failed to load existing tests for course",
			"course_id", courseID,
			"test_id", testID,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return fmt.Errorf("failed to load existing tests: %w", err)
	}

	// Check if test is already associated
	for _, existingTest := range existingTests {
		if existingTest.ID == testID {
			tx.Rollback()
			duration := time.Since(start)
			slog.Warn("Test already associated with course",
				"course_id", courseID,
				"test_id", testID,
				"course_name", course.Name,
				"test_name", test.Name,
				"duration_ms", duration.Milliseconds(),
			)
			return fmt.Errorf("test with ID %d is already associated with course ID %d", testID, courseID)
		}
	}

	// Associate the test with the course
	if err := tx.Model(&course).Association("Tests").Append(&test); err != nil {
		tx.Rollback()
		duration := time.Since(start)
		slog.Error("Failed to associate test with course",
			"course_id", courseID,
			"test_id", testID,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return fmt.Errorf("failed to associate test with course: %w", err)
	}

	// Commit the transaction
	if err := tx.Commit().Error; err != nil {
		duration := time.Since(start)
		slog.Error("Failed to commit test-course association transaction",
			"course_id", courseID,
			"test_id", testID,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return fmt.Errorf("failed to commit transaction: %w", err)
	}

	duration := time.Since(start)
	slog.Info("Test associated with course successfully",
		"course_id", courseID,
		"course_name", course.Name,
		"test_id", testID,
		"test_name", test.Name,
		"existing_tests_count", len(existingTests),
		"duration_ms", duration.Milliseconds(),
	)
	return nil
}
