CREATE TABLE IF NOT EXISTS courses (
    id SERIAL PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT,
    price integer,
    discount float,
    duration_in_days integer,
    is_free BOOLEAN NOT NULL DEFAULT FALSE,
    course_type VARCHAR(10) NOT NULL CHECK (course_type IN ('IIT-JEE', 'NEET')),
    created_at timestamptz DEFAULT now(),
    updated_at timestamptz DEFAULT now(),
    deleted_at timestamptz
);

CREATE TABLE IF NOT EXISTS users (
    id SERIAL PRIMARY KEY,
    email TEXT UNIQUE NOT NULL,
    full_name TEXT,
    phone_number VARCHAR(20) UNIQUE NOT NULL,
    password_hash TEXT,
    role VARCHAR(50) DEFAULT 'student',
    email_verified BOOLEAN DEFAULT FALSE,
    phone_verified BOOLEAN DEFAULT FALSE,
    contact_address TEXT,
    created_at timestamptz DEFAULT now(),
    updated_at timestamptz DEFAULT now(),
    deleted_at timestamptz
);

CREATE TABLE IF NOT EXISTS students (
    id SERIAL PRIMARY KEY,
    user_id integer REFERENCES users(id) on delete cascade,
    parent_phone varchar(20),
    parent_email varchar(255),
    institute varchar(255),
    class varchar(10) CHECK (class IN ('9th', '10th', '11th', '12th', 'dropper')),
    stream varchar(10) CHECK (stream IN ('IIT-JEE', 'NEET')),
    city_or_town varchar(255),
    state varchar(255),
    created_at timestamptz DEFAULT now(),
    updated_at timestamptz DEFAULT now(),
    deleted_at timestamptz
);

CREATE TABLE IF NOT EXISTS students_courses (
    student_id INTEGER REFERENCES students(id) ON DELETE CASCADE,
    course_id INTEGER REFERENCES courses(id) ON DELETE CASCADE,
    PRIMARY KEY (student_id, course_id),
    created_at timestamptz DEFAULT now(),
    updated_at timestamptz DEFAULT now(),
    deleted_at timestamptz
);

CREATE TABLE IF NOT EXISTS subjects (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) unique NOT NULL,
    display_name text NOT NULL,
    created_at timestamptz DEFAULT now(),
    updated_at timestamptz DEFAULT now(),
    deleted_at timestamptz
);

CREATE TABLE IF NOT EXISTS chapters (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) unique NOT NULL,
    display_name text NOT NULL,
    subject_id INTEGER REFERENCES subjects(id) ON DELETE CASCADE,
    created_at timestamptz DEFAULT now(),
    updated_at timestamptz DEFAULT now(),
    deleted_at timestamptz
);

CREATE TABLE IF NOT EXISTS courses_subjects (
    course_id INTEGER REFERENCES courses(id) ON DELETE CASCADE,
    subject_id INTEGER REFERENCES subjects(id) ON DELETE CASCADE,
    PRIMARY KEY (course_id, subject_id),
    created_at timestamptz DEFAULT now(),
    updated_at timestamptz DEFAULT now(),
    deleted_at timestamptz
);

CREATE TABLE IF NOT EXISTS videos (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) unique NOT NULL,
    display_name text NOT NULL,
    video_url TEXT NOT NULL,
    view_count INTEGER DEFAULT 0,
    chapter_id INTEGER REFERENCES chapters(id) ON DELETE CASCADE,
    created_at timestamptz DEFAULT now(),
    updated_at timestamptz DEFAULT now(),
    deleted_at timestamptz
);

CREATE TABLE IF NOT EXISTS study_materials (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) unique NOT NULL,
    display_name text NOT NULL,
    url TEXT NOT NULL,
    chapter_id INTEGER REFERENCES chapters(id) ON DELETE CASCADE,
    created_at timestamptz DEFAULT now(),
    updated_at timestamptz DEFAULT now(),
    deleted_at timestamptz
);

CREATE TABLE IF NOT EXISTS topics (
    id SERIAL PRIMARY KEY,
    chapter_id INT NOT NULL REFERENCES chapters(id),
    name TEXT NOT NULL,
    created_at timestamptz DEFAULT now(),
    updated_at timestamptz DEFAULT now(),
    deleted_at timestamptz
);

CREATE TABLE IF NOT EXISTS formula_cards (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    image_url TEXT NOT NULL,
    topic_id INTEGER REFERENCES topics(id) ON DELETE CASCADE,
    created_at timestamptz DEFAULT now(),
    updated_at timestamptz DEFAULT now(),
    deleted_at timestamptz
);

CREATE TABLE IF NOT EXISTS previous_year_papers (
    id SERIAL PRIMARY KEY,
    month INTEGER NOT NULL CHECK (month >= 1 AND month <= 12),
    year INTEGER NOT NULL CHECK (year >= 1900 AND year <= 2100),
    pdf_url TEXT NOT NULL,
    exam_type VARCHAR(10) NOT NULL CHECK (exam_type IN ('IIT-JEE', 'NEET')),
    created_at timestamptz DEFAULT now(),
    updated_at timestamptz DEFAULT now(),
    deleted_at timestamptz
);

-- Create index for efficient querying by exam type and year
CREATE INDEX IF NOT EXISTS idx_previous_year_papers_exam_type_year
ON previous_year_papers(exam_type, year DESC);

CREATE TABLE IF NOT EXISTS comments (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    video_id INTEGER REFERENCES videos(id) ON DELETE CASCADE,
    material_id INTEGER REFERENCES study_materials(id) ON DELETE CASCADE,
    comment_text TEXT NOT NULL,
    created_at timestamptz DEFAULT now(),
    updated_at timestamptz DEFAULT now(),
    deleted_at timestamptz
);

CREATE TABLE IF NOT EXISTS responses (
    id SERIAL PRIMARY KEY,
    comment_id INTEGER REFERENCES comments(id) ON DELETE CASCADE,
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    response_text TEXT NOT NULL,
    created_at timestamptz DEFAULT now(),
    updated_at timestamptz DEFAULT now(),
    deleted_at timestamptz
);

CREATE TABLE IF NOT EXISTS difficulties (
    id SERIAL PRIMARY KEY,
    name TEXT NOT NULL UNIQUE,
    created_at timestamptz DEFAULT now(),
    updated_at timestamptz DEFAULT now(),
    deleted_at timestamptz
);

CREATE TABLE IF NOT EXISTS questions (
    id SERIAL PRIMARY KEY,
    topic_id INT NOT NULL REFERENCES topics(id),
    difficulty_id INT NOT NULL REFERENCES difficulties(id),
    question_type VARCHAR(20) CHECK (question_type IN ('text', 'mcq', 'multi-select')) NOT NULL,
    text TEXT NOT NULL,
    image_url TEXT,
    file_url TEXT,
    correct_answer TEXT,
    created_at timestamptz DEFAULT now(),
    updated_at timestamptz DEFAULT now(),
    deleted_at timestamptz
);

CREATE TABLE IF NOT EXISTS options (
    id SERIAL PRIMARY KEY,
    question_id INTEGER REFERENCES questions(id) ON DELETE CASCADE,
    option_text TEXT NOT NULL,
    option_image_url TEXT,
    is_correct BOOLEAN DEFAULT FALSE,
    created_at timestamptz DEFAULT now(),
    updated_at timestamptz DEFAULT now(),
    deleted_at timestamptz
);

CREATE TABLE IF NOT EXISTS section_types (
    id SERIAL PRIMARY KEY,
    name TEXT UNIQUE NOT NULL,
    subject_id INTEGER NOT NULL REFERENCES subjects(id) ON DELETE CASCADE,
    question_count INTEGER NOT NULL,
    positive_marks NUMERIC NOT NULL,
    negative_marks NUMERIC NOT NULL,
    created_at TIMESTAMP DEFAULT now(),
    updated_at TIMESTAMP DEFAULT now(),
    deleted_at timestamptz
);

CREATE TABLE IF NOT EXISTS test_types (
    id SERIAL PRIMARY KEY,
    name TEXT UNIQUE NOT NULL,
    created_at TIMESTAMP DEFAULT now(),
    updated_at TIMESTAMP DEFAULT now(),
    deleted_at timestamptz
);

CREATE TABLE IF NOT EXISTS test_type_section_types (
    test_type_id INTEGER REFERENCES test_types(id) ON DELETE CASCADE,
    section_type_id INTEGER REFERENCES section_types(id) ON DELETE CASCADE,
    PRIMARY KEY (test_type_id, section_type_id),
    created_at timestamptz DEFAULT now(),
    updated_at timestamptz DEFAULT now(),
    deleted_at timestamptz
);

CREATE TABLE IF NOT EXISTS tests (
    id SERIAL PRIMARY KEY,
    name TEXT NOT NULL,
    test_type_id INTEGER NOT NULL REFERENCES test_types(id) ON DELETE SET NULL,
    from_time timestamptz,
    to_time timestamptz,
    active boolean not null,
    description text,
    created_at timestamptz DEFAULT now(),
    updated_at timestamptz DEFAULT now(),
    deleted_at timestamptz
);

CREATE TABLE IF NOT EXISTS sections (
    id SERIAL PRIMARY KEY,
    test_id INT NOT NULL REFERENCES tests(id),
    name TEXT NOT NULL,
    section_type_id INTEGER NOT NULL REFERENCES section_types(id) ON DELETE CASCADE,
    display_name TEXT NOT NULL,
    created_at timestamptz DEFAULT now(),
    updated_at timestamptz DEFAULT now(),
    deleted_at timestamptz
);

CREATE TABLE IF NOT EXISTS sections_questions (
    id SERIAL PRIMARY KEY,
    section_id INT NOT NULL REFERENCES sections(id) ON DELETE CASCADE,
    question_id INT NOT NULL REFERENCES questions(id) ON DELETE CASCADE,
    UNIQUE (section_id, question_id),
    created_at timestamptz DEFAULT now(),
    updated_at timestamptz DEFAULT now(),
    deleted_at timestamptz
);

CREATE TABLE IF NOT EXISTS student_test_marks (
    student_id INTEGER REFERENCES students(id) ON DELETE CASCADE,  -- Reference to the student
    test_id INTEGER REFERENCES tests(id) ON DELETE CASCADE,  -- Reference to the test series
    total_positive_marks INTEGER DEFAULT 0 NOT NULL,  -- Total positive marks for the student in this test series
    total_negative_marks INTEGER DEFAULT 0 NOT NULL,  -- Total negative marks for the student in this test series
    final_marks INTEGER GENERATED ALWAYS AS (total_positive_marks - total_negative_marks) STORED NOT NULL,  -- Net final marks in the test series
    PRIMARY KEY (student_id, test_id),
    created_at timestamptz DEFAULT now(),
    updated_at timestamptz DEFAULT now(),
    deleted_at timestamptz
);

-- Optimized index for test-specific rankings (test_id, final_marks DESC)
-- This enables fast queries like: SELECT * FROM student_test_marks WHERE test_id = ? ORDER BY final_marks DESC
CREATE INDEX IF NOT EXISTS idx_test_final_marks_desc ON student_test_marks (test_id, final_marks DESC);

-- Additional index for deleted_at to support soft deletes efficiently
CREATE INDEX IF NOT EXISTS idx_student_test_marks_deleted_at ON student_test_marks (deleted_at) WHERE deleted_at IS NOT NULL;

CREATE TABLE IF NOT EXISTS tests_questions (
    test_id INTEGER REFERENCES tests(id) ON DELETE CASCADE, 
    question_id INTEGER REFERENCES questions(id) ON DELETE CASCADE,
    test_section_id integer not null,
    serial_number integer not null,
    PRIMARY KEY (test_id, question_id),
    created_at timestamptz DEFAULT now(),
    updated_at timestamptz DEFAULT now(),
    deleted_at timestamptz
 );

CREATE TABLE IF NOT EXISTS test_responses (
    id SERIAL PRIMARY KEY,
    student_id INT REFERENCES students(id) ON DELETE CASCADE,
    test_id INT REFERENCES  tests(id) ON DELETE CASCADE,
    question_id INT REFERENCES questions(id) ON DELETE CASCADE,
    selected_option_ids INT[], -- Array of option_ids for mcq/multi-select
    response_text TEXT, -- Text answer for text type questions
    calculated_score integer,
    is_correct boolean default false,
    created_at timestamptz DEFAULT now(),
    updated_at timestamptz DEFAULT now(),
    deleted_at timestamptz
);

CREATE TABLE IF NOT EXISTS courses_tests (
    test_id INTEGER REFERENCES tests(id) ON DELETE CASCADE,
    course_id INTEGER REFERENCES courses(id) ON DELETE CASCADE,
    PRIMARY KEY (test_id, course_id),
    created_at timestamptz DEFAULT now(),
    updated_at timestamptz DEFAULT now(),
    deleted_at timestamptz
);

-- Transaction tables for course purchases
CREATE TABLE IF NOT EXISTS transactions (
    id SERIAL PRIMARY KEY,
    student_id INTEGER NOT NULL REFERENCES students(id) ON DELETE CASCADE,
    amount INTEGER NOT NULL,
    status VARCHAR(20) NOT NULL CHECK (status IN ('PENDING', 'COMPLETED', 'FAILED', 'CANCELLED')),
    transaction_date TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    payment_method VARCHAR(50),
    payment_reference VARCHAR(255),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    deleted_at TIMESTAMPTZ
);

CREATE TABLE IF NOT EXISTS transaction_courses (
    transaction_id INTEGER REFERENCES transactions(id) ON DELETE CASCADE,
    course_id INTEGER REFERENCES courses(id) ON DELETE CASCADE,
    PRIMARY KEY (transaction_id, course_id),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    deleted_at TIMESTAMPTZ
);

-- Indexes for transaction tables
CREATE INDEX IF NOT EXISTS idx_transactions_student_id ON transactions(student_id);
CREATE INDEX IF NOT EXISTS idx_transactions_status ON transactions(status);
CREATE INDEX IF NOT EXISTS idx_transactions_date ON transactions(transaction_date);
CREATE INDEX IF NOT EXISTS idx_transactions_deleted_at ON transactions(deleted_at) WHERE deleted_at IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_transaction_courses_deleted_at ON transaction_courses(deleted_at) WHERE deleted_at IS NOT NULL;

CREATE TABLE IF NOT EXISTS institutions (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    city_or_town VARCHAR(255) NOT NULL,
    state VARCHAR(255) NOT NULL,
    contact_name VARCHAR(255) NOT NULL,
    contact_number VARCHAR(20) NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    deleted_at TIMESTAMPTZ
);

-- Indexes for institutions table
CREATE INDEX IF NOT EXISTS idx_institutions_name ON institutions(name);
CREATE INDEX IF NOT EXISTS idx_institutions_state ON institutions(state);
CREATE INDEX IF NOT EXISTS idx_institutions_city_or_town ON institutions(city_or_town);
CREATE INDEX IF NOT EXISTS idx_institutions_deleted_at ON institutions(deleted_at) WHERE deleted_at IS NOT NULL;

-- Indexes for chapters table
CREATE INDEX IF NOT EXISTS idx_chapters_subject_id ON chapters(subject_id);
CREATE INDEX IF NOT EXISTS idx_chapters_deleted_at ON chapters(deleted_at) WHERE deleted_at IS NOT NULL;

-- Indexes for videos table
CREATE INDEX IF NOT EXISTS idx_videos_chapter_id ON videos(chapter_id);
CREATE INDEX IF NOT EXISTS idx_videos_updated_at ON videos(updated_at DESC);
CREATE INDEX IF NOT EXISTS idx_videos_deleted_at ON videos(deleted_at) WHERE deleted_at IS NOT NULL;

-- Indexes for study_materials table
CREATE INDEX IF NOT EXISTS idx_study_materials_chapter_id ON study_materials(chapter_id);
CREATE INDEX IF NOT EXISTS idx_study_materials_updated_at ON study_materials(updated_at DESC);
CREATE INDEX IF NOT EXISTS idx_study_materials_deleted_at ON study_materials(deleted_at) WHERE deleted_at IS NOT NULL;

-- Indexes for topics table
CREATE INDEX IF NOT EXISTS idx_topics_chapter_id ON topics(chapter_id);
CREATE INDEX IF NOT EXISTS idx_topics_deleted_at ON topics(deleted_at) WHERE deleted_at IS NOT NULL;

-- Indexes for questions table
CREATE INDEX IF NOT EXISTS idx_questions_topic_id ON questions(topic_id);
CREATE INDEX IF NOT EXISTS idx_questions_difficulty_id ON questions(difficulty_id);
CREATE INDEX IF NOT EXISTS idx_questions_deleted_at ON questions(deleted_at) WHERE deleted_at IS NOT NULL;

-- Indexes for options table
CREATE INDEX IF NOT EXISTS idx_options_question_id ON options(question_id);
CREATE INDEX IF NOT EXISTS idx_options_deleted_at ON options(deleted_at) WHERE deleted_at IS NOT NULL;

-- Indexes for comments table
CREATE INDEX IF NOT EXISTS idx_comments_video_id ON comments(video_id);
CREATE INDEX IF NOT EXISTS idx_comments_material_id ON comments(material_id);
CREATE INDEX IF NOT EXISTS idx_comments_user_id ON comments(user_id);
CREATE INDEX IF NOT EXISTS idx_comments_deleted_at ON comments(deleted_at) WHERE deleted_at IS NOT NULL;

-- Indexes for responses table
CREATE INDEX IF NOT EXISTS idx_responses_comment_id ON responses(comment_id);
CREATE INDEX IF NOT EXISTS idx_responses_user_id ON responses(user_id);
CREATE INDEX IF NOT EXISTS idx_responses_deleted_at ON responses(deleted_at) WHERE deleted_at IS NOT NULL;

-- Indexes for test_responses table
CREATE INDEX IF NOT EXISTS idx_test_responses_test_id ON test_responses(test_id);
CREATE INDEX IF NOT EXISTS idx_test_responses_student_id ON test_responses(student_id);
CREATE INDEX IF NOT EXISTS idx_test_responses_question_id ON test_responses(question_id);
CREATE INDEX IF NOT EXISTS idx_test_responses_deleted_at ON test_responses(deleted_at) WHERE deleted_at IS NOT NULL;

-- Indexes for formula_cards table
CREATE INDEX IF NOT EXISTS idx_formula_cards_topic_id ON formula_cards(topic_id);
CREATE INDEX IF NOT EXISTS idx_formula_cards_deleted_at ON formula_cards(deleted_at) WHERE deleted_at IS NOT NULL;

-- Indexes for courses_tests junction table
CREATE INDEX IF NOT EXISTS idx_courses_tests_course_id ON courses_tests(course_id);
CREATE INDEX IF NOT EXISTS idx_courses_tests_test_id ON courses_tests(test_id);
CREATE INDEX IF NOT EXISTS idx_courses_tests_deleted_at ON courses_tests(deleted_at) WHERE deleted_at IS NOT NULL;

-- Indexes for test_types table
CREATE INDEX IF NOT EXISTS idx_test_types_name ON test_types(name);
CREATE INDEX IF NOT EXISTS idx_test_types_deleted_at ON test_types(deleted_at) WHERE deleted_at IS NOT NULL;

-- Indexes for students table
CREATE INDEX IF NOT EXISTS idx_students_user_id ON students(user_id);
CREATE INDEX IF NOT EXISTS idx_students_stream ON students(stream);
CREATE INDEX IF NOT EXISTS idx_students_class ON students(class);
CREATE INDEX IF NOT EXISTS idx_students_created_at ON students(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_students_deleted_at ON students(deleted_at) WHERE deleted_at IS NOT NULL;

-- Indexes for users table (for prefix searches)
CREATE INDEX IF NOT EXISTS idx_users_full_name_prefix ON users(full_name text_pattern_ops);
CREATE INDEX IF NOT EXISTS idx_users_email_prefix ON users(email text_pattern_ops);
CREATE INDEX IF NOT EXISTS idx_users_phone_number_prefix ON users(phone_number text_pattern_ops);
CREATE INDEX IF NOT EXISTS idx_users_deleted_at ON users(deleted_at) WHERE deleted_at IS NOT NULL;